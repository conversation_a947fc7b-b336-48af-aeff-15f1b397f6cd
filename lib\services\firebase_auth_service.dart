import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user.dart' as app_user;

class FirebaseAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Auth state changes stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Convert Firebase User to App User
  app_user.User? _userFromFirebaseUser(User? user) {
    if (user == null) return null;
    
    return app_user.User(
      id: user.uid,
      email: user.email ?? '',
      fullName: user.displayName ?? 'User',
      createdAt: user.metadata.creationTime ?? DateTime.now(),
    );
  }

  // Get current app user
  Future<app_user.User?> getCurrentUser() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      // Get additional user data from Firestore
      final doc = await _firestore.collection('users').doc(user.uid).get();
      if (doc.exists) {
        final data = doc.data()!;
        return app_user.User(
          id: user.uid,
          email: user.email ?? '',
          fullName: data['fullName'] ?? user.displayName ?? 'User',
          createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? 
                     user.metadata.creationTime ?? DateTime.now(),
        );
      }
    } catch (e) {
      print('Error getting user data: $e');
    }

    return _userFromFirebaseUser(user);
  }

  // Sign up with email and password
  Future<app_user.User?> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = credential.user;
      if (user != null) {
        // Update display name
        await user.updateDisplayName(fullName);

        // Save additional user data to Firestore
        await _firestore.collection('users').doc(user.uid).set({
          'email': email,
          'fullName': fullName,
          'createdAt': FieldValue.serverTimestamp(),
          'lastLoginAt': FieldValue.serverTimestamp(),
        });

        return app_user.User(
          id: user.uid,
          email: email,
          fullName: fullName,
          createdAt: DateTime.now(),
        );
      }
    } catch (e) {
      print('Sign up error: $e');
      rethrow;
    }
    return null;
  }

  // Sign in with email and password
  Future<app_user.User?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = credential.user;
      if (user != null) {
        // Update last login time
        await _firestore.collection('users').doc(user.uid).update({
          'lastLoginAt': FieldValue.serverTimestamp(),
        });

        return await getCurrentUser();
      }
    } catch (e) {
      print('Sign in error: $e');
      rethrow;
    }
    return null;
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      print('Sign out error: $e');
      rethrow;
    }
  }

  // Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      print('Reset password error: $e');
      rethrow;
    }
  }

  // Delete account
  Future<void> deleteAccount() async {
    try {
      final user = currentUser;
      if (user != null) {
        // Delete user data from Firestore
        await _firestore.collection('users').doc(user.uid).delete();
        
        // Delete user predictions
        final predictions = await _firestore
            .collection('predictions')
            .where('userId', isEqualTo: user.uid)
            .get();
        
        for (final doc in predictions.docs) {
          await doc.reference.delete();
        }

        // Delete Firebase Auth account
        await user.delete();
      }
    } catch (e) {
      print('Delete account error: $e');
      rethrow;
    }
  }

  // Check if email exists
  Future<bool> isEmailRegistered(String email) async {
    try {
      final methods = await _auth.fetchSignInMethodsForEmail(email);
      return methods.isNotEmpty;
    } catch (e) {
      print('Check email error: $e');
      return false;
    }
  }
}
