import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/prediction_result.dart';
import '../models/user.dart' as app_user;

class FirebaseDatabaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Get current user ID
  String? get _currentUserId => _auth.currentUser?.uid;

  // Save prediction result
  Future<String> savePredictionResult(PredictionResult prediction) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final docRef = await _firestore.collection('predictions').add({
        'userId': userId,
        'prediction': prediction.prediction,
        'confidence': prediction.confidence,
        'symptoms': prediction.symptoms,
        'riskLevel': prediction.riskLevel,
        'recommendations': prediction.recommendations,
        'exposureLocations': prediction.exposureLocations,
        'locationWarning': prediction.locationWarning,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      return docRef.id;
    } catch (e) {
      print('Error saving prediction: $e');
      rethrow;
    }
  }

  // Get user's prediction history
  Future<List<PredictionResult>> getUserPredictions() async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        return [];
      }

      final querySnapshot = await _firestore
          .collection('predictions')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        return PredictionResult(
          id: doc.id,
          userId: data['userId'],
          prediction: data['prediction'] ?? '',
          confidence: (data['confidence'] ?? 0.0).toDouble(),
          symptoms: Map<String, dynamic>.from(data['symptoms'] ?? {}),
          riskLevel: data['riskLevel'] ?? 'Unknown',
          recommendations: List<String>.from(data['recommendations'] ?? []),
          exposureLocations: data['exposureLocations'] != null
              ? Map<String, String>.from(data['exposureLocations'])
              : null,
          locationWarning: data['locationWarning'] != null
              ? Map<String, dynamic>.from(data['locationWarning'])
              : null,
          createdAt:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        );
      }).toList();
    } catch (e) {
      print('Error getting predictions: $e');
      return [];
    }
  }

  // Get recent predictions (limited)
  Future<List<PredictionResult>> getRecentPredictions({int limit = 5}) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        return [];
      }

      final querySnapshot = await _firestore
          .collection('predictions')
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        return PredictionResult(
          id: doc.id,
          userId: data['userId'],
          prediction: data['prediction'] ?? '',
          confidence: (data['confidence'] ?? 0.0).toDouble(),
          symptoms: Map<String, dynamic>.from(data['symptoms'] ?? {}),
          riskLevel: data['riskLevel'] ?? 'Unknown',
          recommendations: List<String>.from(data['recommendations'] ?? []),
          exposureLocations: data['exposureLocations'] != null
              ? Map<String, String>.from(data['exposureLocations'])
              : null,
          locationWarning: data['locationWarning'] != null
              ? Map<String, dynamic>.from(data['locationWarning'])
              : null,
          createdAt:
              (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
        );
      }).toList();
    } catch (e) {
      print('Error getting recent predictions: $e');
      return [];
    }
  }

  // Delete prediction
  Future<void> deletePrediction(String predictionId) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      // Verify the prediction belongs to the current user
      final doc = await _firestore
          .collection('predictions')
          .doc(predictionId)
          .get();
      if (!doc.exists) {
        throw Exception('Prediction not found');
      }

      final data = doc.data()!;
      if (data['userId'] != userId) {
        throw Exception('Unauthorized access');
      }

      await _firestore.collection('predictions').doc(predictionId).delete();
    } catch (e) {
      print('Error deleting prediction: $e');
      rethrow;
    }
  }

  // Get prediction statistics
  Future<Map<String, dynamic>> getPredictionStats() async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        return {
          'totalPredictions': 0,
          'riskDistribution': <String, int>{},
          'lastPredictionDate': null,
        };
      }

      final querySnapshot = await _firestore
          .collection('predictions')
          .where('userId', isEqualTo: userId)
          .get();

      final predictions = querySnapshot.docs;
      final riskDistribution = <String, int>{};
      DateTime? lastPredictionDate;

      for (final doc in predictions) {
        final data = doc.data();
        final riskLevel = data['riskLevel'] ?? 'Unknown';
        riskDistribution[riskLevel] = (riskDistribution[riskLevel] ?? 0) + 1;

        final createdAt = (data['createdAt'] as Timestamp?)?.toDate();
        if (createdAt != null) {
          if (lastPredictionDate == null ||
              createdAt.isAfter(lastPredictionDate)) {
            lastPredictionDate = createdAt;
          }
        }
      }

      return {
        'totalPredictions': predictions.length,
        'riskDistribution': riskDistribution,
        'lastPredictionDate': lastPredictionDate,
      };
    } catch (e) {
      print('Error getting prediction stats: $e');
      return {
        'totalPredictions': 0,
        'riskDistribution': <String, int>{},
        'lastPredictionDate': null,
      };
    }
  }

  // Update user profile
  Future<void> updateUserProfile(app_user.User user) async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      await _firestore.collection('users').doc(userId).update({
        'fullName': user.fullName,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update Firebase Auth display name
      await _auth.currentUser?.updateDisplayName(user.fullName);
    } catch (e) {
      print('Error updating user profile: $e');
      rethrow;
    }
  }

  // Get user profile
  Future<app_user.User?> getUserProfile() async {
    try {
      final userId = _currentUserId;
      if (userId == null) {
        return null;
      }

      final doc = await _firestore.collection('users').doc(userId).get();
      if (!doc.exists) {
        return null;
      }

      final data = doc.data()!;
      return app_user.User(
        id: userId,
        email: data['email'] ?? '',
        fullName: data['fullName'] ?? '',
        createdAt:
            (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      );
    } catch (e) {
      print('Error getting user profile: $e');
      return null;
    }
  }
}
